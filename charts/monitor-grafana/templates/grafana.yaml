{{- $fullName := include "monitor-grafana.fullname" . -}}
{{- if .Values.grafana.enable }}
---
apiVersion: v1
kind: Service
metadata:
  name: grafana-{{ $fullName }}
  labels:
    {{- include "monitor-grafana.labels" . | nindent 4 }}
    app.kubernetes.io/component: grafana
spec:
  type: ClusterIP
  selector:
    {{- include "monitor-grafana.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: grafana
  ports:
    - port: 3000
      protocol: TCP
      targetPort: 3000
  sessionAffinity: None
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana-{{ $fullName }}
  labels:
    {{- include "monitor-grafana.labels" . | nindent 4 }}
    app.kubernetes.io/component: grafana
spec:
  replicas: {{ .Values.grafana.replicaCount }}
  selector:
    matchLabels:
      {{- include "monitor-grafana.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: grafana
  template:
    metadata:
      labels:
        {{- include "monitor-grafana.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: grafana
    spec:
      containers:
      - args:
        - --homepath=/usr/share/grafana
        - --config=/etc/grafana/grafana.ini
        env:
        - name: GF_SECURITY_ADMIN_PASSWORD
          value: {{ .Values.grafana.password }}
        - name: GF_DATABASE_TYPE
          value: {{ .Values.grafana.database.type }}
        - name: GF_DATABASE_HOST
          value: {{ .Values.grafana.database.host }}
        - name: GF_DATABASE_USER
          value: {{ .Values.grafana.database.user }}
        - name: GF_DATABASE_PASSWORD
          value: {{ .Values.grafana.database.password }}
        - name: GF_DATABASE_NAME
          value: {{ .Values.grafana.database.dbname }}
        image: "{{.Values.grafana.image.registry}}/{{.Values.grafana.image.image}}:{{.Values.grafana.image.tag}}"
        imagePullPolicy: Always
        lifecycle:
          postStart:
            exec:
              command:
              - /bin/sh
              - -c
              - | 
                cp -r /cce/grafana/provisioning/* /etc/grafana/provisioning/
                sleep 5
                grafana-cli admin reset-admin-password ${GF_SECURITY_ADMIN_PASSWORD}
        name: grafana
        ports:
        - containerPort: 3000
          name: http-grafana
          protocol: TCP
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /robots.txt
            port: 3000
            scheme: HTTP
          initialDelaySeconds: 10
          periodSeconds: 30
          successThreshold: 1
          timeoutSeconds: 2
{{- with .Values.grafana.resources }}
        resources:
        {{- toYaml . | nindent 10 }}
{{- end }}
        securityContext:
          runAsUser: 0
        volumeMounts:
        - mountPath: /cce/grafana/provisioning
          name: cce-grafana-provisoning
      dnsPolicy: ClusterFirst
      initContainers:
      - command:
        - /bin/sh
        - -c
        - cp -r /grafana/provisioning/* /cce/grafana/provisioning/
        image: "{{.Values.grafana.provisioningImage.registry}}/{{.Values.grafana.provisioningImage.image}}:{{.Values.grafana.provisioningImage.tag}}"
        imagePullPolicy: Always
        name: cce-grafana-provisioning
        resources: {}
        volumeMounts:
        - mountPath: /cce/grafana/provisioning
          name: cce-grafana-provisoning
      restartPolicy: Always
      volumes:
      - emptyDir: {}
        name: cce-grafana-provisoning
{{- end}}