package monitor_instance

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/runtime"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"
	controllerruntime "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	fakeclient "sigs.k8s.io/controller-runtime/pkg/client/fake"

	ccrv1alpha1 "icode.baidu.com/baidu/cprom/cloud-stack/pkg/apis/ccr/v1alpha1"
	cpromv1 "icode.baidu.com/baidu/cprom/cloud-stack/pkg/apis/cprom/v1"
	vmv1beta1 "icode.baidu.com/baidu/cprom/cloud-stack/pkg/apis/vm/v1beta1"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/models"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/mysql/vm_manage"
	vm_managemock "icode.baidu.com/baidu/cprom/cloud-stack/pkg/mysql/vm_manage/mock"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/utils"
)

var ctx *gin.Context

// TestMonitorInstanceAPI_GetNamespace 测试函数GetNamespace，获取新实例的命名空间
func TestMonitorInstanceAPI_GetNamespace(t *testing.T) {
	ctl := gomock.NewController(t)
	type args struct {
		newInstanceId string
		accountManage vm_manage.Interface
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "case1: get ns",
			args: args{
				newInstanceId: "1",
				accountManage: func() vm_manage.Interface {
					mockInterface := vm_managemock.NewMockInterface(ctl)
					mockInterface.EXPECT().QueryByNewInstanceId(gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
						FNamespace: "default",
					}, nil)
					return mockInterface
				}(),
			},
			want: "default",
		},
		{
			name: "case1: get ns error",
			args: args{
				newInstanceId: "1",
				accountManage: func() vm_manage.Interface {
					mockInterface := vm_managemock.NewMockInterface(ctl)
					mockInterface.EXPECT().QueryByNewInstanceId(gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
						FNamespace: "default",
					}, fmt.Errorf("get account error"))
					return mockInterface
				}(),
			},
			want: "default",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, _ = GetNamespace(tt.args.newInstanceId, tt.args.accountManage)
		})
	}
}

// TestMonitorInstanceAPI_deleteToken 删除token
func TestMonitorInstanceAPI_deleteToken(t *testing.T) {
	type fields struct {
		accountManage      vm_manage.Interface
		clientSet          client.Client
		c                  *gin.Context
		newMonitorInstance string
	}

	tests := []struct {
		name   string
		fields fields
	}{
		{
			name: "case1: token is empty",
			fields: func() fields {
				ctl := gomock.NewController(t)
				mockInterface := vm_managemock.NewMockInterface(ctl)
				mockInterface.EXPECT().QueryByNewInstanceId(
					gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
					FNamespace: "default",
				}, nil).AnyTimes()
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "2c0ebe60-ca4be206040f")
				authTokenBody := strings.NewReader(`{
	"token": ""
}`)
				req := httptest.NewRequest("POST", "/", authTokenBody) // 请求参数
				header := http.Header{}
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req

				return fields{
					accountManage:      mockInterface,
					clientSet:          nil,
					c:                  ctx,
					newMonitorInstance: "",
				}
			}(),
		},
		{
			name: "case1: token parse failed",
			fields: func() fields {
				ctl := gomock.NewController(t)
				mockInterface := vm_managemock.NewMockInterface(ctl)
				mockInterface.EXPECT().QueryByNewInstanceId(
					gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
					FNamespace: "default",
				}, nil).AnyTimes()
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "2c0ebe60-ca4be206040f")
				req := httptest.NewRequest("POST", "/", strings.NewReader("")) // 请求参数
				header := http.Header{}
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req

				return fields{
					accountManage:      mockInterface,
					clientSet:          nil,
					c:                  ctx,
					newMonitorInstance: "",
				}
			}(),
		},
		{
			name: "case1: not found token before delete, secret list size = 0",
			fields: func() fields {
				ctl := gomock.NewController(t)
				mockInterface := vm_managemock.NewMockInterface(ctl)
				mockInterface.EXPECT().QueryByNewInstanceId(
					gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
					FNamespace: "default",
				}, nil).AnyTimes()
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "2c0ebe60-ca4be206040f")
				authTokenBody := strings.NewReader(`{
	"token": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************.ADpGfSbXrHHH8YQF-NH15nNCoUKezqanKvgP9onyyQM"
}`)
				req := httptest.NewRequest("POST", "/", authTokenBody) // 请求参数
				header := http.Header{}
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req

				cli := fakeclient.NewFakeClientWithScheme(schemeForTests(),
					&cpromv1.MonitorInstance{
						ObjectMeta: controllerruntime.ObjectMeta{
							Name:      "monitor-instance-xxx",
							Namespace: "default",
							Labels: map[string]string{
								cpromv1.BCEAccountIDLabel: "2c0ebe60-ca4be206040f",
							},
						},
					},
				)

				return fields{
					accountManage:      mockInterface,
					clientSet:          cli,
					c:                  ctx,
					newMonitorInstance: "cprom-rvy7mhyt14z31",
				}
			}(),
		},
		{
			name: "case1: token not belong to current mi",
			fields: func() fields {
				ctl := gomock.NewController(t)
				mockInterface := vm_managemock.NewMockInterface(ctl)
				mockInterface.EXPECT().QueryByNewInstanceId(
					gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
					FNamespace: "default",
				}, nil).AnyTimes()
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "2c0ebe60-ca4be206040f")
				authTokenBody := strings.NewReader(`{
	"token": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************.ADpGfSbXrHHH8YQF-NH15nNCoUKezqanKvgP9onyyQM"
}`)
				req := httptest.NewRequest("POST", "/", authTokenBody) // 请求参数
				header := http.Header{}
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req

				cli := fakeclient.NewFakeClientWithScheme(schemeForTests(),
					&cpromv1.MonitorInstance{
						ObjectMeta: controllerruntime.ObjectMeta{
							Name:      "monitor-instance-xxx",
							Namespace: "default",
							Labels: map[string]string{
								cpromv1.BCEAccountIDLabel: "2c0ebe60-ca4be206040f",
							},
						},
					},
				)

				return fields{
					accountManage:      mockInterface,
					clientSet:          cli,
					c:                  ctx,
					newMonitorInstance: "",
				}
			}(),
		},
		{
			name: "case1: not found token before delete, secret list size > 0",
			fields: func() fields {
				ctl := gomock.NewController(t)
				mockInterface := vm_managemock.NewMockInterface(ctl)
				mockInterface.EXPECT().QueryByNewInstanceId(
					gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
					FNamespace: "default",
				}, nil).AnyTimes()
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "2c0ebe60-ca4be206040f")
				authTokenBody := strings.NewReader(`{
	"token": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************.ADpGfSbXrHHH8YQF-NH15nNCoUKezqanKvgP9onyyQM"
}`)
				req := httptest.NewRequest("POST", "/", authTokenBody) // 请求参数
				header := http.Header{}
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req

				cli := fakeclient.NewFakeClientWithScheme(schemeForTests(),
					&v1.Secret{
						ObjectMeta: controllerruntime.ObjectMeta{
							Name:      "secret-xxx0",
							Namespace: "default",
							Labels: map[string]string{
								"token-new-instance-id": "cprom-rvy7mhyt14z31",
								"token-secret-name":     "2e1be1eb99e946c3a543ec5a4eaa7d39",
								"token-type":            models.AccountTokenType,
								"token-hash":            utils.GenHash("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************.ADpGfSbXrHHH8YQF-NH15nNCoUKezqanKvgP9onyyQM"),
							},
						},
					},
					&v1.Secret{
						ObjectMeta: controllerruntime.ObjectMeta{
							Name:      "secret-xxx1",
							Namespace: "default",
							Labels: map[string]string{
								"token-new-instance-id": "cprom-rvy7mhyt14z31",
								"token-secret-name":     "2e1be1eb99e946c3a543ec5a4eaa7d39",
								"token-type":            models.AccountTokenType,
								"token-hash":            utils.GenHash("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************.ADpGfSbXrHHH8YQF-NH15nNCoUKezqanKvgP9onyyQM"),
							},
						},
					},
				)

				return fields{
					accountManage:      mockInterface,
					clientSet:          cli,
					c:                  ctx,
					newMonitorInstance: "cprom-rvy7mhyt14z31",
				}
			}(),
		},
		{
			name: "case1: get namespace failed",
			fields: func() fields {
				ctl := gomock.NewController(t)
				mockInterface := vm_managemock.NewMockInterface(ctl)
				mockInterface.EXPECT().QueryByNewInstanceId(
					gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
					FNamespace: "default",
				}, fmt.Errorf("get namespace failed")).AnyTimes()
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "2c0ebe60-ca4be206040f")
				authTokenBody := strings.NewReader(`{
	"token": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************.ADpGfSbXrHHH8YQF-NH15nNCoUKezqanKvgP9onyyQM"
}`)
				req := httptest.NewRequest("POST", "/", authTokenBody) // 请求参数
				header := http.Header{}
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req

				return fields{
					accountManage: mockInterface,
					clientSet:     nil,
					c:             ctx,
				}
			}(),
		},
		{
			name: "case1: token parse failed",
			fields: func() fields {
				ctl := gomock.NewController(t)
				mockInterface := vm_managemock.NewMockInterface(ctl)
				mockInterface.EXPECT().QueryByNewInstanceId(
					gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
					FNamespace: "default",
				}, nil).AnyTimes()
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "2c0ebe60-ca4be206040f")
				authTokenBody := strings.NewReader(`{
	"token": "xxx"
}`)
				req := httptest.NewRequest("POST", "/", authTokenBody) // 请求参数
				header := http.Header{}
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req

				return fields{
					accountManage: mockInterface,
					clientSet:     nil,
					c:             ctx,
				}
			}(),
		},
		{
			name: "case1: vm-agent token delete",
			fields: func() fields {
				ctl := gomock.NewController(t)
				mockInterface := vm_managemock.NewMockInterface(ctl)
				mockInterface.EXPECT().QueryByNewInstanceId(
					gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
					FNamespace: "default",
				}, nil).AnyTimes()
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "2c0ebe60-ca4be206040f")
				authTokenBody := strings.NewReader(`{
	"token": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************.ehVvClZMHdufQXqZBuTIQnnujQ7tDfiyK6SsmxazDr4"
}`)
				req := httptest.NewRequest("POST", "/", authTokenBody) // 请求参数
				header := http.Header{}
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req

				cli := fakeclient.NewFakeClientWithScheme(schemeForTests())

				return fields{
					accountManage: mockInterface,
					clientSet:     cli,
					c:             ctx,
				}
			}(),
		},
		{
			name: "case1: vm-agent token can not delete",
			fields: func() fields {
				ctl := gomock.NewController(t)
				mockInterface := vm_managemock.NewMockInterface(ctl)
				mockInterface.EXPECT().QueryByNewInstanceId(
					gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
					FNamespace: "cprom-zvpuubq17",
				}, nil).AnyTimes()
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "2c0ebe60-ca4be206040f")
				authTokenBody := strings.NewReader(`{
	"token": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************.ehVvClZMHdufQXqZBuTIQnnujQ7tDfiyK6SsmxazDr4"
}`)
				req := httptest.NewRequest("POST", "/", authTokenBody) // 请求参数
				header := http.Header{}
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req

				cli := fakeclient.NewFakeClientWithScheme(schemeForTests(),
					&cpromv1.MonitorAgent{
						ObjectMeta: controllerruntime.ObjectMeta{
							Name:      "agent-z715pkrt7",
							Namespace: "cprom-zvpuubq17",
						},
					},
				)

				return fields{
					accountManage: mockInterface,
					clientSet:     cli,
					c:             ctx,
				}
			}(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &MonitorInstanceAPI{
				accountManage: tt.fields.accountManage,
				clientSet:     tt.fields.clientSet,
			}

			h.once.Do(func() {
				h.clientSet = tt.fields.clientSet
			})

			h.deleteToken(tt.fields.c, tt.fields.newMonitorInstance)

		})
	}
}

func schemeForTests() *runtime.Scheme {
	var scheme = runtime.NewScheme()
	utilruntime.Must(clientgoscheme.AddToScheme(scheme))
	utilruntime.Must(cpromv1.AddToScheme(scheme))
	utilruntime.Must(ccrv1alpha1.AddToScheme(scheme))
	utilruntime.Must(vmv1beta1.AddToScheme(scheme))
	return scheme
}

// TestMonitorInstanceAPI_genUserToken 测试 MonitorInstanceAPI 的 genUserToken 方法
func TestMonitorInstanceAPI_genUserToken(t *testing.T) {
	type fields struct {
		accountManage vm_manage.Interface
		clientSet     client.Client
		c             *gin.Context
	}

	tests := []struct {
		name   string
		fields fields
	}{
		{
			name: "case: request bind failed",
			fields: func() fields {
				ctl := gomock.NewController(t)
				mockInterface := vm_managemock.NewMockInterface(ctl)
				mockInterface.EXPECT().QueryByNewInstanceId(
					gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
					FNamespace: "cprom-mi",
				}, nil).AnyTimes()
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "xxx-xxx")
				req := httptest.NewRequest("POST", "/", strings.NewReader("")) // 请求参数

				header := http.Header{}
				// 注意不设置请求头，c.Bind() 解析不出来 json 参数
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req

				cli := fakeclient.NewFakeClientWithScheme(schemeForTests())

				return fields{
					accountManage: mockInterface,
					clientSet:     cli,
					c:             ctx,
				}
			}(),
		},
		{
			name: "case: request is empty",
			fields: func() fields {
				ctl := gomock.NewController(t)
				mockInterface := vm_managemock.NewMockInterface(ctl)
				mockInterface.EXPECT().QueryByNewInstanceId(
					gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
					FNamespace: "cprom-mi",
				}, nil).AnyTimes()
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "xxx-xxx")
				authTokenBody := strings.NewReader(`{
}`)
				req := httptest.NewRequest("POST", "/", authTokenBody) // 请求参数

				header := http.Header{}
				// 注意不设置请求头，c.Bind() 解析不出来 json 参数
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req

				cli := fakeclient.NewFakeClientWithScheme(schemeForTests())

				return fields{
					accountManage: mockInterface,
					clientSet:     cli,
					c:             ctx,
				}
			}(),
		},
		{
			name: "case: accountId check failed",
			fields: func() fields {
				ctl := gomock.NewController(t)
				mockInterface := vm_managemock.NewMockInterface(ctl)
				mockInterface.EXPECT().QueryByNewInstanceId(
					gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
					FNamespace: "cprom-mi",
				}, nil).AnyTimes()
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "xxx-xxx-xxx")
				authTokenBody := strings.NewReader(`{
"bceAccountId": "xxx-xxx",
"monitorInstanceId":"new-mi-xxx"
}`)
				req := httptest.NewRequest("POST", "/", authTokenBody) // 请求参数

				header := http.Header{}
				// 注意不设置请求头，c.Bind() 解析不出来 json 参数
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req

				cli := fakeclient.NewFakeClientWithScheme(schemeForTests())

				return fields{
					accountManage: mockInterface,
					clientSet:     cli,
					c:             ctx,
				}
			}(),
		},
		{
			name: "case: get namespace failed",
			fields: func() fields {
				ctl := gomock.NewController(t)
				mockInterface := vm_managemock.NewMockInterface(ctl)
				mockInterface.EXPECT().QueryByNewInstanceId(
					gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
					FNamespace: "cprom-mi",
				}, fmt.Errorf("get namespace failed")).AnyTimes()
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "xxx-xxx")
				authTokenBody := strings.NewReader(`{
"bceAccountId": "xxx-xxx",
"monitorInstanceId":"new-mi-xxxxxxxxxxxxx"
}`)
				req := httptest.NewRequest("POST", "/", authTokenBody) // 请求参数

				header := http.Header{}
				// 注意不设置请求头，c.Bind() 解析不出来 json 参数
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req

				cli := fakeclient.NewFakeClientWithScheme(schemeForTests())

				return fields{
					accountManage: mockInterface,
					clientSet:     cli,
					c:             ctx,
				}
			}(),
		},
		{
			name: "case: get namespace succeed",
			fields: func() fields {
				ctl := gomock.NewController(t)
				mockInterface := vm_managemock.NewMockInterface(ctl)
				mockInterface.EXPECT().QueryByNewInstanceId(
					gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
					FNamespace: "cprom-mi",
				}, nil).AnyTimes()
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "xxx-xxx")
				authTokenBody := strings.NewReader(`{
"bceAccountId": "xxx-xxx",
"monitorInstanceId":"new-mi-xxxxxxxxxxxxx"
}`)
				req := httptest.NewRequest("POST", "/", authTokenBody) // 请求参数

				header := http.Header{}
				// 注意不设置请求头，c.Bind() 解析不出来 json 参数
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req

				cli := fakeclient.NewFakeClientWithScheme(schemeForTests())

				return fields{
					accountManage: mockInterface,
					clientSet:     cli,
					c:             ctx,
				}
			}(),
		},
		{
			name: "case: do create auth token",
			fields: func() fields {
				ctl := gomock.NewController(t)
				mockInterface := vm_managemock.NewMockInterface(ctl)
				mockInterface.EXPECT().QueryByNewInstanceId(
					gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
					FNamespace: "cprom-mi",
				}, nil).AnyTimes()
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "xxx-xxx")
				authTokenBody := strings.NewReader(`{
"bceAccountId": "xxx-xxx",
"monitorInstanceId":"new-mi-xxx"
}`)
				req := httptest.NewRequest("POST", "/", authTokenBody) // 请求参数

				header := http.Header{}
				// 注意不设置请求头，c.Bind() 解析不出来 json 参数
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req

				cli := fakeclient.NewFakeClientWithScheme(schemeForTests())

				return fields{
					accountManage: mockInterface,
					clientSet:     cli,
					c:             ctx,
				}
			}(),
		},
		{
			name: "case: check mi disable",
			fields: func() fields {
				ctl := gomock.NewController(t)
				mockInterface := vm_managemock.NewMockInterface(ctl)
				mockInterface.EXPECT().QueryByNewInstanceId(
					gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
					FNamespace:     "cprom-mi",
					FNewInstanceId: "new-mi-xxx",
					FBceAccount:    "account-xxx",
				}, nil).AnyTimes()
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "xxx-xxx")
				authTokenBody := strings.NewReader(`{
"bceAccountId": "account-xxx",
"monitorInstanceId":"new-mi-xxx"
}`)
				req := httptest.NewRequest("POST", "/", authTokenBody) // 请求参数

				header := http.Header{}
				// 注意不设置请求头，c.Bind() 解析不出来 json 参数
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req

				ctx.Params = gin.Params{gin.Param{Key: "ID", Value: "new-mi-xxx"}}
				cli := fakeclient.NewFakeClientWithScheme(schemeForTests())

				return fields{
					accountManage: mockInterface,
					clientSet:     cli,
					c:             ctx,
				}
			}(),
		},
		{
			name: "case: check mi account not equal",
			fields: func() fields {
				ctl := gomock.NewController(t)
				mockInterface := vm_managemock.NewMockInterface(ctl)
				mockInterface.EXPECT().QueryByNewInstanceId(
					gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
					FNamespace:     "cprom-mi",
					FNewInstanceId: "new-mi-xxx",
					FBceAccount:    "account-xxx",
					FCpromDisable:  1,
				}, nil).AnyTimes()
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "xxx-xxx")
				authTokenBody := strings.NewReader(`{}`)
				req := httptest.NewRequest("POST", "/", authTokenBody) // 请求参数

				header := http.Header{}
				// 注意不设置请求头，c.Bind() 解析不出来 json 参数
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req

				ctx.Params = gin.Params{gin.Param{Key: "ID", Value: "new-mi-xxx"}}
				cli := fakeclient.NewFakeClientWithScheme(schemeForTests())

				return fields{
					accountManage: mockInterface,
					clientSet:     cli,
					c:             ctx,
				}
			}(),
		},
		{
			name: "case: check mi account is nil",
			fields: func() fields {
				ctl := gomock.NewController(t)
				mockInterface := vm_managemock.NewMockInterface(ctl)
				mockInterface.EXPECT().QueryByNewInstanceId(
					gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "xxx-xxx")
				authTokenBody := strings.NewReader(`{}`)
				req := httptest.NewRequest("POST", "/", authTokenBody) // 请求参数

				header := http.Header{}
				// 注意不设置请求头，c.Bind() 解析不出来 json 参数
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req

				ctx.Params = gin.Params{gin.Param{Key: "ID", Value: "new-mi-xxx"}}
				cli := fakeclient.NewFakeClientWithScheme(schemeForTests())

				return fields{
					accountManage: mockInterface,
					clientSet:     cli,
					c:             ctx,
				}
			}(),
		},
		{
			name: "case: check mi err",
			fields: func() fields {
				ctl := gomock.NewController(t)
				mockInterface := vm_managemock.NewMockInterface(ctl)
				mockInterface.EXPECT().QueryByNewInstanceId(
					gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("")).AnyTimes()
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "xxx-xxx")
				authTokenBody := strings.NewReader(`{}`)
				req := httptest.NewRequest("POST", "/", authTokenBody) // 请求参数

				header := http.Header{}
				// 注意不设置请求头，c.Bind() 解析不出来 json 参数
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req

				ctx.Params = gin.Params{gin.Param{Key: "ID", Value: "new-mi-xxx"}}
				cli := fakeclient.NewFakeClientWithScheme(schemeForTests())

				return fields{
					accountManage: mockInterface,
					clientSet:     cli,
					c:             ctx,
				}
			}(),
		},
		{
			name: "case: check mi succeed",
			fields: func() fields {
				ctl := gomock.NewController(t)
				mockInterface := vm_managemock.NewMockInterface(ctl)
				mockInterface.EXPECT().QueryByNewInstanceId(
					gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
					FNamespace:     "cprom-mi",
					FNewInstanceId: "new-mi-xxx",
					FBceAccount:    "account-xxx",
					FCpromDisable:  1,
				}, nil).AnyTimes()
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "account-xxx")
				authTokenBody := strings.NewReader(`{}`)
				req := httptest.NewRequest("POST", "/", authTokenBody) // 请求参数

				header := http.Header{}
				// 注意不设置请求头，c.Bind() 解析不出来 json 参数
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req

				ctx.Params = gin.Params{gin.Param{Key: "ID", Value: "new-mi-xxx"}}
				cli := fakeclient.NewFakeClientWithScheme(schemeForTests())

				return fields{
					accountManage: mockInterface,
					clientSet:     cli,
					c:             ctx,
				}
			}(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &MonitorInstanceAPI{
				accountManage: tt.fields.accountManage,
				clientSet:     tt.fields.clientSet,
			}

			h.once.Do(func() {
				h.clientSet = tt.fields.clientSet
			})

			h.genUserToken(tt.fields.c)
		})
	}
}

// TestMonitorInstanceAPI_listToken 测试 MonitorInstanceAPI 的 listToken 函数
func TestMonitorInstanceAPI_listToken(t *testing.T) {
	type fields struct {
		accountManage vm_manage.Interface
		clientSet     client.Client
		c             *gin.Context
	}

	tests := []struct {
		name   string
		fields fields
	}{
		{
			name: "case: accountId check failed",
			fields: func() fields {
				ctl := gomock.NewController(t)
				mockInterface := vm_managemock.NewMockInterface(ctl)
				mockInterface.EXPECT().QueryByNewInstanceId(
					gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
					FNamespace: "cprom-mi",
				}, nil).AnyTimes()
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "xxx-xxx-xxx")
				authTokenBody := strings.NewReader(`{
"bceAccountId": "xxx-xxx",
"monitorInstanceId":"new-mi-xxx"
}`)
				req := httptest.NewRequest("POST", "/", authTokenBody) // 请求参数

				header := http.Header{}
				// 注意不设置请求头，c.Bind() 解析不出来 json 参数
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req

				cli := fakeclient.NewFakeClientWithScheme(schemeForTests())

				return fields{
					accountManage: mockInterface,
					clientSet:     cli,
					c:             ctx,
				}
			}(),
		},
		{
			name: "case: request bind failed",
			fields: func() fields {
				ctl := gomock.NewController(t)
				mockInterface := vm_managemock.NewMockInterface(ctl)
				mockInterface.EXPECT().QueryByNewInstanceId(
					gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
					FNamespace: "cprom-mi",
				}, nil).AnyTimes()
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "xxx-xxx")
				req := httptest.NewRequest("POST", "/", strings.NewReader("")) // 请求参数

				header := http.Header{}
				// 注意不设置请求头，c.Bind() 解析不出来 json 参数
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req

				cli := fakeclient.NewFakeClientWithScheme(schemeForTests())

				return fields{
					accountManage: mockInterface,
					clientSet:     cli,
					c:             ctx,
				}
			}(),
		},
		{
			name: "case: request is empty",
			fields: func() fields {
				ctl := gomock.NewController(t)
				mockInterface := vm_managemock.NewMockInterface(ctl)
				mockInterface.EXPECT().QueryByNewInstanceId(
					gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
					FNamespace: "cprom-mi",
				}, nil).AnyTimes()
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "xxx-xxx")
				authTokenBody := strings.NewReader(`{
}`)
				req := httptest.NewRequest("POST", "/", authTokenBody) // 请求参数

				header := http.Header{}
				// 注意不设置请求头，c.Bind() 解析不出来 json 参数
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req

				cli := fakeclient.NewFakeClientWithScheme(schemeForTests())

				return fields{
					accountManage: mockInterface,
					clientSet:     cli,
					c:             ctx,
				}
			}(),
		},
		{
			name: "case: get namespace failed",
			fields: func() fields {
				ctl := gomock.NewController(t)
				mockInterface := vm_managemock.NewMockInterface(ctl)
				mockInterface.EXPECT().QueryByNewInstanceId(
					gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
					FNamespace: "cprom-mi",
				}, fmt.Errorf("get namespace failed")).AnyTimes()
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "xxx-xxx")
				authTokenBody := strings.NewReader(`{
"bceAccountId": "xxx-xxx",
"monitorInstanceId":"new-mi-xxx"
}`)
				req := httptest.NewRequest("POST", "/", authTokenBody) // 请求参数

				header := http.Header{}
				// 注意不设置请求头，c.Bind() 解析不出来 json 参数
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req

				cli := fakeclient.NewFakeClientWithScheme(schemeForTests())

				return fields{
					accountManage: mockInterface,
					clientSet:     cli,
					c:             ctx,
				}
			}(),
		},
		{
			name: "case: secret size > 0",
			fields: func() fields {
				ctl := gomock.NewController(t)
				mockInterface := vm_managemock.NewMockInterface(ctl)
				mockInterface.EXPECT().QueryByNewInstanceId(
					gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
					FNamespace: "default",
				}, nil).AnyTimes()
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "2e1be1eb99e946c3a543ec5a4eaa7d39")
				authTokenBody := strings.NewReader(`{
"bceAccountId": "2e1be1eb99e946c3a543ec5a4eaa7d39",
"monitorInstanceId":"cprom-rvy7mhyt14z31"
}`)
				req := httptest.NewRequest("POST", "/", authTokenBody) // 请求参数

				header := http.Header{}
				// 注意不设置请求头，c.Bind() 解析不出来 json 参数
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req

				cli := fakeclient.NewFakeClientWithScheme(schemeForTests(),
					&v1.Secret{
						ObjectMeta: controllerruntime.ObjectMeta{
							Name:      "secret-xxx0",
							Namespace: "default",
							Labels: map[string]string{
								"token-new-instance-id": "cprom-rvy7mhyt14z31",
								"token-secret-name":     "2e1be1eb99e946c3a543ec5a4eaa7d39",
								"token-type":            models.AccountTokenType,
								"token-hash":            utils.GenHash("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************.ADpGfSbXrHHH8YQF-NH15nNCoUKezqanKvgP9onyyQM"),
							},
						},
					},
					&v1.Secret{
						ObjectMeta: controllerruntime.ObjectMeta{
							Name:      "secret-xxx1",
							Namespace: "default",
							Labels: map[string]string{
								"token-new-instance-id": "cprom-rvy7mhyt14z31",
								"token-secret-name":     "2e1be1eb99e946c3a543ec5a4eaa7d39",
								"token-type":            models.AccountTokenType,
								"token-hash":            utils.GenHash("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************.ADpGfSbXrHHH8YQF-NH15nNCoUKezqanKvgP9onyyQM"),
							},
						},
					},
				)

				return fields{
					accountManage: mockInterface,
					clientSet:     cli,
					c:             ctx,
				}
			}(),
		},
		{
			name: "case: check mi account not equal",
			fields: func() fields {
				ctl := gomock.NewController(t)
				mockInterface := vm_managemock.NewMockInterface(ctl)
				mockInterface.EXPECT().QueryByNewInstanceId(
					gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
					FNamespace:     "default",
					FBceAccount:    "account-xxx",
					FCpromDisable:  1,
					FNewInstanceId: "new-mi-xxx",
				}, nil).AnyTimes()
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "xxx-account")
				authTokenBody := strings.NewReader(`{}`)
				req := httptest.NewRequest("POST", "/", authTokenBody) // 请求参数

				header := http.Header{}
				// 注意不设置请求头，c.Bind() 解析不出来 json 参数
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req
				ctx.Params = gin.Params{gin.Param{Key: "ID", Value: "new-mi-xxx"}}

				cli := fakeclient.NewFakeClientWithScheme(schemeForTests(),
					&v1.Secret{
						ObjectMeta: controllerruntime.ObjectMeta{
							Name:      "secret-xxx0",
							Namespace: "default",
							Labels: map[string]string{
								"token-new-instance-id": "cprom-rvy7mhyt14z31",
								"token-secret-name":     "2e1be1eb99e946c3a543ec5a4eaa7d39",
								"token-type":            models.AccountTokenType,
								"token-hash":            utils.GenHash("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************.ADpGfSbXrHHH8YQF-NH15nNCoUKezqanKvgP9onyyQM"),
							},
						},
					},
					&v1.Secret{
						ObjectMeta: controllerruntime.ObjectMeta{
							Name:      "secret-xxx1",
							Namespace: "default",
							Labels: map[string]string{
								"token-new-instance-id": "cprom-rvy7mhyt14z31",
								"token-secret-name":     "2e1be1eb99e946c3a543ec5a4eaa7d39",
								"token-type":            models.AccountTokenType,
								"token-hash":            utils.GenHash("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************.ADpGfSbXrHHH8YQF-NH15nNCoUKezqanKvgP9onyyQM"),
							},
						},
					},
				)

				return fields{
					accountManage: mockInterface,
					clientSet:     cli,
					c:             ctx,
				}
			}(),
		},
		{
			name: "case: check mi succeed",
			fields: func() fields {
				ctl := gomock.NewController(t)
				mockInterface := vm_managemock.NewMockInterface(ctl)
				mockInterface.EXPECT().QueryByNewInstanceId(
					gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
					FNamespace:     "default",
					FBceAccount:    "2e1be1eb99e946c3a543ec5a4eaa7d39",
					FCpromDisable:  1,
					FNewInstanceId: "cprom-rvy7mhyt14z31",
				}, nil).AnyTimes()
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "2e1be1eb99e946c3a543ec5a4eaa7d39")
				authTokenBody := strings.NewReader(`{}`)
				req := httptest.NewRequest("POST", "/", authTokenBody) // 请求参数

				header := http.Header{}
				// 注意不设置请求头，c.Bind() 解析不出来 json 参数
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req
				ctx.Params = gin.Params{gin.Param{Key: "ID", Value: "cprom-rvy7mhyt14z31"}}

				cli := fakeclient.NewFakeClientWithScheme(schemeForTests(),
					&v1.Secret{
						ObjectMeta: controllerruntime.ObjectMeta{
							Name:      "secret-xxx0",
							Namespace: "default",
							Labels: map[string]string{
								"token-new-instance-id": "cprom-rvy7mhyt14z31",
								"token-secret-name":     "2e1be1eb99e946c3a543ec5a4eaa7d39",
								"token-type":            models.AccountTokenType,
								"token-hash":            utils.GenHash("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************.ADpGfSbXrHHH8YQF-NH15nNCoUKezqanKvgP9onyyQM"),
							},
						},
					},
					&v1.Secret{
						ObjectMeta: controllerruntime.ObjectMeta{
							Name:      "secret-xxx1",
							Namespace: "default",
							Labels: map[string]string{
								"token-new-instance-id": "cprom-rvy7mhyt14z31",
								"token-secret-name":     "2e1be1eb99e946c3a543ec5a4eaa7d39",
								"token-type":            models.AccountTokenType,
								"token-hash":            utils.GenHash("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************.ADpGfSbXrHHH8YQF-NH15nNCoUKezqanKvgP9onyyQM"),
							},
						},
					},
				)

				return fields{
					accountManage: mockInterface,
					clientSet:     cli,
					c:             ctx,
				}
			}(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &MonitorInstanceAPI{
				accountManage: tt.fields.accountManage,
				clientSet:     tt.fields.clientSet,
			}

			h.once.Do(func() {
				h.clientSet = tt.fields.clientSet
			})

			h.listToken(tt.fields.c)
		})
	}
}

// TestMonitorInstanceAPI_getTokenQuota 测试 MonitorInstanceAPI_getTokenQuota 函数
func TestMonitorInstanceAPI_getTokenQuota(t *testing.T) {
	type fields struct {
		c             *gin.Context
		accountManage vm_manage.Interface
	}

	tests := []struct {
		name   string
		fields fields
	}{
		{
			name: "case: succeed",
			fields: func() fields {
				ctl := gomock.NewController(t)
				mockInterface := vm_managemock.NewMockInterface(ctl)
				mockInterface.EXPECT().QueryByNewInstanceId(
					gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
					FNamespace:     "cprom-mi",
					FBceAccount:    "account-xxx",
					FCpromDisable:  1,
					FNewInstanceId: "cprom-xxx",
				}, nil).AnyTimes()
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "account-xxx")
				authTokenBody := strings.NewReader(`{}`)
				req := httptest.NewRequest("POST", "/", authTokenBody) // 请求参数

				header := http.Header{}
				// 注意不设置请求头，c.Bind() 解析不出来 json 参数
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req
				ctx.Params = gin.Params{gin.Param{Key: "ID", Value: "cprom-xxx"}}

				return fields{
					accountManage: mockInterface,
					c:             ctx,
				}
			}(),
		},
		{
			name: "case: check mi failed",
			fields: func() fields {
				ctl := gomock.NewController(t)
				mockInterface := vm_managemock.NewMockInterface(ctl)
				mockInterface.EXPECT().QueryByNewInstanceId(
					gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
					FNamespace:     "cprom-mi",
					FBceAccount:    "account",
					FCpromDisable:  1,
					FNewInstanceId: "cprom-xxx",
				}, nil).AnyTimes()
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "account-xxx")
				authTokenBody := strings.NewReader(`{}`)
				req := httptest.NewRequest("POST", "/", authTokenBody) // 请求参数

				header := http.Header{}
				// 注意不设置请求头，c.Bind() 解析不出来 json 参数
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req
				ctx.Params = gin.Params{gin.Param{Key: "ID", Value: "cprom-xxx"}}

				return fields{
					accountManage: mockInterface,
					c:             ctx,
				}
			}(),
		},
		{
			name: "case: check account is emoty",
			fields: func() fields {
				ctl := gomock.NewController(t)
				mockInterface := vm_managemock.NewMockInterface(ctl)
				mockInterface.EXPECT().QueryByNewInstanceId(
					gomock.Any(), gomock.Any()).Return(&vm_manage.TAccountManage{
					FNamespace:     "cprom-mi",
					FBceAccount:    "account",
					FCpromDisable:  1,
					FNewInstanceId: "cprom-xxx",
				}, nil).AnyTimes()
				ctx, _ = gin.CreateTestContext(httptest.NewRecorder())
				ctx.Set("x-bce-request-id", "2c0ebe60-1d3f-40bb-bf00-ca4be206040f")
				ctx.Set("accountID", "")
				authTokenBody := strings.NewReader(`{}`)
				req := httptest.NewRequest("POST", "/", authTokenBody) // 请求参数

				header := http.Header{}
				// 注意不设置请求头，c.Bind() 解析不出来 json 参数
				header.Set("Content-Type", "application/json")
				req.Header = header
				ctx.Request = req
				ctx.Params = gin.Params{gin.Param{Key: "ID", Value: "cprom-xxx"}}

				return fields{
					accountManage: mockInterface,
					c:             ctx,
				}
			}(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &MonitorInstanceAPI{
				accountManage: tt.fields.accountManage,
			}
			h.getTokenQuota(tt.fields.c)
		})
	}
}

// TestJWTTokenParsing 测试JWT token解析和过期时间计算
func TestJWTTokenParsing(t *testing.T) {
	// 测试JWT token解析和过期时间计算

	// 生成一个测试token，过期时间为1天后
	namespace := "cprom-jr3861uf397o4"
	secretName := "eca97e148cb74e9683d7b7240829d1ff"
	expireDuration := time.Hour * 24 // 1天

	token, err := utils.GenToken(namespace, secretName, expireDuration)
	if err != nil {
		t.Fatalf("Failed to generate token: %v", err)
	}

	t.Logf("Generated token: %s", token)

	// 解析token
	claims, err := utils.ParseToken(token)
	if err != nil {
		t.Fatalf("Failed to parse token: %v", err)
	}

	t.Logf("Parsed claims: namespace=%s, secretName=%s, exp=%d",
		claims.Namespace, claims.SecretName, claims.ExpiresAt)

	// 计算剩余过期天数
	expireTime := time.Unix(claims.ExpiresAt, 0)
	now := time.Now()
	daysUntilExpiry := int(expireTime.Sub(now).Hours() / 24)

	t.Logf("Days until expiry: %d", daysUntilExpiry)

	// 验证结果
	if claims.Namespace != namespace {
		t.Errorf("Expected namespace %s, got %s", namespace, claims.Namespace)
	}

	if claims.SecretName != secretName {
		t.Errorf("Expected secretName %s, got %s", secretName, claims.SecretName)
	}

	// 应该大约是1天（允许一些误差）
	if daysUntilExpiry < 0 || daysUntilExpiry > 1 {
		t.Errorf("Expected days until expiry to be around 1, got %d", daysUntilExpiry)
	}
}

// TestExpiredToken 测试已过期的token
func TestExpiredToken1(t *testing.T) {
	// 测试已过期的token

	namespace := "test-namespace"
	secretName := "test-secret"
	// 生成一个已经过期的token（过期时间为1小时前）
	expireDuration := -time.Hour

	token, err := utils.GenToken(namespace, secretName, expireDuration)
	if err != nil {
		t.Fatalf("Failed to generate expired token: %v, token: %s", err, token)
	}
	t.Logf("Generated expired token: %s", token)

	// 解析token
	claims, err := utils.ParseToken(token)
	if err != nil {
		t.Logf("Failed to parse expired token: %v, claims: %v", err, claims)
	}
}

func TestExpiredToken2(t *testing.T) {
	// 测试已过期的token

	namespace := "test-namespace"
	secretName := "test-secret"
	// 生成一个已经过期的token（过期时间为1小时前）
	expireDuration := time.Hour

	token, err := utils.GenToken(namespace, secretName, expireDuration)
	if err != nil {
		t.Fatalf("Failed to generate expired token: %v", err)
	}

	// 解析token
	claims, err := utils.ParseToken(token)
	if err != nil {
		t.Fatalf("Failed to parse expired token: %v", err)
	}

	// 计算剩余过期天数
	expireTime := time.Unix(claims.ExpiresAt, 0)
	now := time.Now()
	daysUntilExpiry := int(expireTime.Sub(now).Hours() / 24)

	t.Logf("Days until expiry for expired token: %d", daysUntilExpiry)

	// 应该是负数，表示已过期
	if daysUntilExpiry >= 0 {
		t.Logf("Expected negative days for expired token, got %d", daysUntilExpiry)
	}
}
