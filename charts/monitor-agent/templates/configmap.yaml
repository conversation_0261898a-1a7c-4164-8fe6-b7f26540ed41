{{- $fullName := include "monitor-agent.fullname" . -}}
---
kind: ConfigMap
apiVersion: v1
metadata:
  name: {{ $fullName }}
  namespace: cprom-system
data:
  prometheus.yml: |-
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
    scrape_config_files:
    - /etc/prometheus/scrape_configs/*.yml
  sidecar.yml: |
    sidecar:
      runMode: debug
      listenAddr: 0.0.0.0:8080
      accessToken: {{.Values.accessToken }}
      managerAddress: {{.Values.managerAddress }}
      agentAddress: http://127.0.0.1:8429

---
apiVersion: v1
data:
  root.pem: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUNFekNDQWJxZ0F3SUJBZ0lVTXZocXZJb2toMEJaN3hyNlc1eC9Ud2dlTnlrd0NnWUlLb1pJemowRUF3SXcKWnpFTE1Ba0dBMVVFQmhNQ1EwNHhFREFPQmdOVkJBZ1RCMEpsYVVwcGJtY3hFREFPQmdOVkJBY1RCMEpsYVVwcApibWN4RGpBTUJnTlZCQW9UQldwd1lXRnpNUlF3RWdZRFZRUUxFd3RqYkc5MVpHNWhkR2wyWlRFT01Bd0dBMVVFCkF4TUZhbkJoWVhNd0lCY05NakV3T1RFMU1UWXhOakF3V2hnUE1qRXlNVEE0TWpJeE5qRTJNREJhTUdjeEN6QUoKQmdOVkJBWVRBa05PTVJBd0RnWURWUVFJRXdkQ1pXbEthVzVuTVJBd0RnWURWUVFIRXdkQ1pXbEthVzVuTVE0dwpEQVlEVlFRS0V3VnFjR0ZoY3pFVU1CSUdBMVVFQ3hNTFkyeHZkV1J1WVhScGRtVXhEakFNQmdOVkJBTVRCV3B3CllXRnpNRmt3RXdZSEtvWkl6ajBDQVFZSUtvWkl6ajBEQVFjRFFnQUVNU1Z4SEhNZklXVEU5SEdraXNraXc2cTMKRlAvSkEyWDFVbU0xSTU4ajZwNS9NR0NmdVlsT1l2M2FBTEVNSXg4OEhNbk9HTTdoRVh6QXhUQmxWSnVGanFOQwpNRUF3RGdZRFZSMFBBUUgvQkFRREFnRUdNQThHQTFVZEV3RUIvd1FGTUFNQkFmOHdIUVlEVlIwT0JCWUVGRmQ4CjBrZUR5VXlVRTd4ZzBXaTh5NnJvbWFuL01Bb0dDQ3FHU000OUJBTUNBMGNBTUVRQ0lGSllDVXJpaU1vUEFrSjgKNUxYMVlMa081UWwrUmVuOWx3cFJlTXRGeDBWcEFpQlU5VEJnYnJ4clpHZHdlTlZRRkZ1bllzK3E1dVlPQUMzZgo5SWpnWDBvT3B3PT0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=
  telegraf-client-key.pem: ****************************************************************************************************************************************************************************************************************************************************************************************************************
  telegraf-client.pem: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUNaRENDQWdxZ0F3SUJBZ0lVUkEwdlBqUFVPbEhJSnZJVW9ZZ0pEdHIyeGNnd0NnWUlLb1pJemowRUF3SXcKWnpFTE1Ba0dBMVVFQmhNQ1EwNHhFREFPQmdOVkJBZ1RCMEpsYVVwcGJtY3hFREFPQmdOVkJBY1RCMEpsYVVwcApibWN4RGpBTUJnTlZCQW9UQldwd1lXRnpNUlF3RWdZRFZRUUxFd3RqYkc5MVpHNWhkR2wyWlRFT01Bd0dBMVVFCkF4TUZhbkJoWVhNd0lCY05NakV3T1RFMU1UWXhOekF3V2hnUE1qRXlNVEE0TWpJeE5qRTNNREJhTUhFeEN6QUoKQmdOVkJBWVRBa05PTVJBd0RnWURWUVFJRXdkQ1pXbEthVzVuTVJBd0RnWURWUVFIRXdkQ1pXbEthVzVuTVE0dwpEQVlEVlFRS0V3VnFjR0ZoY3pFVU1CSUdBMVVFQ3hNTFkyeHZkV1J1WVhScGRtVXhHREFXQmdOVkJBTVREM1JsCmJHVm5jbUZtTFdOc2FXVnVkREJaTUJNR0J5cUdTTTQ5QWdFR0NDcUdTTTQ5QXdFSEEwSUFCQ0h0VUJsWWlWSEcKeEJINHUrOGRhZXpxNTZTY1NMcTFwMlhyZmZZOE1TdHV1ekJ3Z2c2WXV2TkVnSnM5TDMrQXM5U1lveXdGU1d0Rwo3M2hHNDV3K2FXeWpnWWN3Z1lRd0RnWURWUjBQQVFIL0JBUURBZ1dnTUJNR0ExVWRKUVFNTUFvR0NDc0dBUVVGCkJ3TUNNQXdHQTFVZEV3RUIvd1FDTUFBd0hRWURWUjBPQkJZRUZBR1A0K1BMa05GRGpjNHNVNlgxUmM1ZXBXU3oKTUI4R0ExVWRJd1FZTUJhQUZGZDgwa2VEeVV5VUU3eGcwV2k4eTZyb21hbi9NQThHQTFVZEVRUUlNQWFIQkg4QQpBQUV3Q2dZSUtvWkl6ajBFQXdJRFNBQXdSUUloQUlsVGxIUTF0bEtWQXRUWlBIRm1XZThCYlBvR25IYlFOTWhWCnBISXVSSFg0QWlBQ0xlMDBzc0RYYlBXTGQ0dWVBd1pKSWI2bnNvVGVYY1gxUUdmejUvZ3o3dz09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
kind: Secret
metadata:
  name: {{ $fullName }}
  namespace: cprom-system
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ $fullName }}-etcd
  namespace: cprom-system
data:
  ca.pem: {{.Values.eca }}
  etcd.pem: {{.Values.ecert }}
  etcd-key.pem: {{.Values.ecertKey }}
---