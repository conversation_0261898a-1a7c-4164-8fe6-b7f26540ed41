## charts

云原生监控 charts

### 相关目录
* monitor-instance 该目录封装了 monitor-instance 部署相关的组件，其中包括
    * grafana 监控展示组件
    * vmalert `Recording rules`& `Alert rules` 计算组件
    * alertmanager 告警处理组件
    * vmcluster 监控组件
      * vmselect 监控查询入口组件
      * vminsert 监控数据写入组件
      * vmstorage 监控数据存储组件
* monitor-agent 该目录封装了 monitor-agent 相关部署组件，其中包括
  * vmagent 监控采集组件
  * cprom-sidecar (agent sidecar 组件，负责配置更新等等功能。)

### monitor-instance 测试部署测试

* 修改 `monitor-instance/values.yaml` 配置
* 创建测试 ns `kubectl create ns test` 并在集群里面提前创建好 storageClass, [参考](/cce-stack/DOC/设计文档/可观测性/云原生监控/yaml/storageclass.yaml)
* 执行 `helm install zihua ./monitor-instance  --namespace test`

> 注⚠️：集群里面需要安装 nginx-ingress-controller 才能通过 grafana 验证


