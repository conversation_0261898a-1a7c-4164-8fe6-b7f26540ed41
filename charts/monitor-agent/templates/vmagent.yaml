{{- $fullName := include "monitor-agent.fullname" . -}}
{{- if .Values.vmagent.enable }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ $fullName }}
rules:
  - apiGroups: [""]
    resources:
      - persistentvolumeclaims
    verbs:
      - delete
  - apiGroups:
      - apps
    resources:
      - statefulsets
    verbs:
      - list
      - get
      - patch
      - update
  - apiGroups: [""]
    resources:
      - nodes
      - nodes/metrics
      - services
      - endpoints
      - pods
      - configmaps
      - secrets
    verbs: ["get", "list", "watch"]
  - nonResourceURLs: ["/metrics","/metrics/cadvisor"]
    verbs: ["get"]
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ $fullName }}
  namespace: cprom-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ $fullName }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ $fullName }}
subjects:
  - kind: ServiceAccount
    name: {{ $fullName }}
    namespace: cprom-system
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    {{- include "monitor-agent.labels" . | nindent 4 }}
    app.kubernetes.io/component: vmagent
  name: {{ $fullName }}
  namespace: cprom-system
spec:
  podManagementPolicy: Parallel
  replicas: {{.Values.vmagent.replicaCount}}
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      {{- include "monitor-agent.labels" . | nindent 6 }}
      app.kubernetes.io/component: vmagent
  serviceName: {{ $fullName }}
  template:
    metadata:
      labels:
        {{- include "monitor-agent.labels" . | nindent 8 }}
        app.kubernetes.io/component: vmagent
      annotations:
        prometheus.io/port: "8429"
        prometheus.io/scrape: "true"
    spec:
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              preference:
                matchExpressions:
                  - key: beta.kubernetes.io/instance-gpu
                    operator: In
                    values:
                      - "false"
      containers:
        - name: sidecar
          env:
            - name: SCRAPE_CONFIGS_PATH
              value: /etc/prometheus/scrape_configs
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: NAMESPACE
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.namespace
          args:
            - --config=/etc/prometheus/config/sidecar.yml
          image: "{{.Values.sidecar.image.registry}}/{{.Values.sidecar.image.image}}:{{.Values.sidecar.image.tag}}"
          imagePullPolicy: Always
          {{- with .Values.sidecar.resources }}
          resources:
          {{- toYaml . | nindent 12 }}
          {{- end }}
          volumeMounts:
            - mountPath: /etc/prometheus/config
              name: config
            - mountPath: /etc/prometheus/scrape_configs
              name: scrape-configs
        - name: vmagent
          env:
            - name: REPLICA_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: promscrape_cluster_memberPodName
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.name
          args:
            - -envflag.enable=true
            - -promscrape.cluster.membersCount={{.Values.vmagent.replicaCount}}
            - -promscrape.cluster.memberNum=$(promscrape_cluster_memberPodName)
            - -promscrape.configCheckInterval=30s
            - -promscrape.streamParse=true
            - -promscrape.maxScrapeSize=500MiB
            - -remoteWrite.label=agentID={{.Values.agentID}}
            {{- range $key, $value := .Values.vmagent.externalFlagLabels}}
            - -remoteWrite.label={{$key}}={{$value}}
            {{- end }}
            - -remoteWrite.label=clusterID={{.Values.clusterID}}
            - -remoteWrite.label=region={{.Values.region}}
            - -promscrape.config=/etc/prometheus/config/prometheus.yml
            - -promscrape.suppressDuplicateScrapeTargetErrors=true
            - -remoteWrite.url={{.Values.remoteWriteAddress}}
            - -promscrape.config.strictParse=false
            - -remoteWrite.headers=InstanceId:{{.Values.instanceID}}
            - -remoteWrite.bearerToken={{.Values.accessToken}}
          image: "{{.Values.vmagent.image.registry}}/{{.Values.vmagent.image.image}}:{{.Values.vmagent.image.tag}}"
          imagePullPolicy: Always
          ports:
            - name: http
              protocol: TCP
              containerPort: 8429
          {{- with .Values.vmagent.resources }}
          resources:
          {{- toYaml . | nindent 12 }}
          {{- end }}
          volumeMounts:
            - mountPath: /etc/prometheus/config
              name: config
            - mountPath: /etc/prometheus/scrape_configs
              name: scrape-configs
            - mountPath: /etc/prometheus/cert
              name: telegraf-client-cert
            - mountPath: /etc/prometheus/etcd/cert
              name: config-etcd
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext:
        runAsUser: 0
      serviceAccount: {{ $fullName }}
      serviceAccountName: {{ $fullName }}
      terminationGracePeriodSeconds: 30
      volumes:
        - secret:
            secretName: {{ $fullName }}-etcd
          name: config-etcd
        - configMap:
            defaultMode: 420
            name: {{ $fullName }}
          name: config
        - name: scrape-configs
          emptyDir: {}
        - secret:
            secretName: {{ $fullName }}
          name: telegraf-client-cert
  updateStrategy:
    rollingUpdate:
      partition: 0
    type: RollingUpdate
  {{- end}}