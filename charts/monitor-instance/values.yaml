fullnameOverride: cprom-zihua
storageClass: baiducds-vm # 需要提前创建好 storageClass
configReload:
  image:
    registry: registry.baidubce.com
    image: cce-plugin-dev/configmap-reload
    tag: v0.3.0

vmcluster:
  replicationFactor: 2 # vmstorage 分片冗余数
  retentionPeriod: 1 # 存储时长，单位为 月
  vminsert:
    extraArgs:
      maxLabelsPerTimeseries: "100" # vminsert 启动参数，这里需要积累经验
    image:
      registry: registry.baidubce.com
      image: cce-plugin-dev/vminsert
      tag: v1.79.0-cluster
    replicaCount: 2 # insert 实例数
    resources:
      requests:
        cpu: "100m"
        memory: "128Mi"
      limits:
        cpu: "100m"
        memory: "128Mi"
  vmselect:
    extraArgs:
      dedup.minScrapeInterval: "1ms"
      search.maxSamplesPerQuery: "300000000"
      search.maxSamplesPerSeries: "10000000"
      search.maxUniqueTimeseries: "100000"
    image:
      registry: registry.baidubce.com
      image: cce-plugin-dev/vmselect
      tag: v1.79.0-cluster
    replicaCount: 2 # select 实例数
    resources:
      requests:
        cpu: "100m"
        memory: "128Mi"
      limits:
        cpu: "100m"
        memory: "128Mi"
    dataVolumeSize: 50Gi # 缓存卷大小
  vmstorage:
    extraArgs:
      search.maxUniqueTimeseries: "100000"
      dedup.minScrapeInterval: "1ms"
    image:
      registry: registry.baidubce.com
      image: cce-plugin-dev/vmstorage
      tag: v1.79.0-cluster
    replicaCount: 2 # storage 实例数
    resources:
      requests:
        cpu: "100m"
        memory: "128Mi"
      limits:
        cpu: "100m"
        memory: "128Mi"
    dataVolumeSize: 50Gi # 缓存卷大小
