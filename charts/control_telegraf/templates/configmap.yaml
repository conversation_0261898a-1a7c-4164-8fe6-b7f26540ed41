apiVersion: v1
data:
  telegraf.conf: |
    [global_tags]
      clusterID = "{{.Release.Namespace}}"
    [agent]
      collection_jitter = "0s"
      debug = false
      flush_interval = "10s"
      flush_jitter = "0s"
      hostname = "$HOSTNAME"
      interval = "10s"
      logfile = ""
      metric_batch_size = 1000
      metric_buffer_limit = 10000
      omit_hostname = false
      precision = ""
      quiet = false
      round_interval = true

    # 类似于 metrics_relabel
    # 在 telegraf 中指标由三部分构成 name, field, tag
    # name_field[tags] name_field 拼接成 指标名称，如kubernetes_pod_container_resource_limits_millicpu_units
    # 其中kubernetes_pod_container为 name, resource_limits_millicpu_units为 field，tag 相当于 prometheus 中的 label
    # kubernetes_pod_container_resource_limits_millicpu_units -> container_limit_cpu_millicores
    # kubernetes_pod_container_resource_limits_memory_bytes -> container_limit_memory_bytes
    [[processors.rename]]
    [[processors.rename.replace]]
      measurement = "kubernetes_pod_container"
      dest = "container"
    [[processors.rename.replace]]
      field = "resource_limits_millicpu_units"
      dest = "cpu_millicores"
    [[processors.rename.replace]]
      field = "resource_limits_memory_bytes"
      dest = "memory_bytes"

    [[processors.filter]]
      default = "drop"
    [[processors.filter.rule]]
      tags = {"container_name" = ["apiserver","controller-manager","scheduler","etcd"]}
      action = "pass"

    [[inputs.kube_inventory]]
      namespace="{{.Release.Namespace}}"
      resource_include=["pods"]
      fieldinclude = ["resource_limits_millicpu_units","resource_limits_memory_bytes"]
      tagexclude = ["node_name","image","namespace","phase","readiness","state","version","host"]

    [[inputs.kubernetes]]
      label_include=["pro-clusterID"]
      namepass=["kubernetes_pod_container"]
      tagexclude = ["node_name","image","version","host"]
      fieldinclude = ["cpu_usage_nanocores","cpu_usage_core_nanoseconds","memory_usage_bytes","memory_working_set_bytes","memory_rss_bytes"]
      [inputs.kubernetes.tagpass]
      namespace = ["{{.Release.Namespace}}"]

    [[outputs.prometheus_client]]
      listen = ":9005"
      path = "/metrics"
      collectors_exclude = ["gocollector", "process"]
      tls_cert = "/etc/kubernetes/pki/telegraf/telegraf.pem"
      tls_key = "/etc/kubernetes/pki/telegraf/telegraf-key.pem"
      tls_allowed_cacerts = ["/etc/kubernetes/pki/telegraf/root.pem"]
kind: ConfigMap
metadata:
  name: {{.Release.Namespace}}-configmap
  namespace: {{ .Release.Namespace }}