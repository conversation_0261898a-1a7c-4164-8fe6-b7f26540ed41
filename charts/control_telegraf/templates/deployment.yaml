apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{.Release.Namespace}}-telegraf
  namespace: {{ .Release.Namespace }}
spec:
  replicas: {{.Values.replicaCount}}
  selector:
    matchLabels:
      app: cprom-telegraf
      app.kubernetes.io/instance: {{.Release.Namespace}}-telegraf
      app.kubernetes.io/name: {{.Release.Namespace}}-telegraf
  template:
    metadata:
      labels:
        app: cprom-telegraf
        app.kubernetes.io/instance: {{.Release.Namespace}}-telegraf
        app.kubernetes.io/name: {{.Release.Namespace}}-telegraf
    spec:
      containers:
        - env:
            - name: HOSTNAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
          image: registry.baidubce.com/cce-plugin-dev/telegraf:v1.29.4
          imagePullPolicy: IfNotPresent
          name: cprom-telegraf
          resources: {}
          volumeMounts:
            - mountPath: /etc/telegraf
              name: config
            - mountPath: /etc/kubernetes/pki/telegraf
              name: telegraf-server-certs
            - mountPath: /etc/telegraf/certs
              name: telegraf-client-certs
      serviceAccountName: {{.Release.Namespace}}-serviceaccount
      volumes:
        - configMap:
            defaultMode: 420
            name: {{.Release.Namespace}}-configmap
          name: config
        - name: telegraf-server-certs
          secret:
            defaultMode: 420
            secretName: telegraf-server-certs
        - name: telegraf-client-certs
          secret:
            defaultMode: 420
            secretName: telegraf-client-certs
