package utils

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"icode.baidu.com/baidu/cprom/cloud-stack/cprom-common/utils"

	"github.com/dgrijalva/jwt-go"
)

var (
	tokenExpireDuration = time.Hour * 24 * 365 * 100
	secret              = []byte("cprom@@@###!!!")
)

// TokenClaims token
type TokenClaims struct {
	Namespace  string `json:"namespace"`
	SecretName string `json:"secretName"`

	jwt.StandardClaims
}

// GenToken 生成JWT
func GenToken(namespace, secretName string, expire time.Duration) (string, error) {
	if expire == 0 {
		expire = tokenExpireDuration
	}
	c := TokenClaims{
		namespace,
		secretName,
		jwt.StandardClaims{
			ExpiresAt: time.Now().Add(expire).Unix(), // 过期时间
			Issuer:    "cprom",                       // 签发人
		},
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, c)
	return token.SignedString(secret)
}

// ParseToken 解析JWT
func ParseToken(tokenString string) (*TokenClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &TokenClaims{}, func(token *jwt.Token) (i interface{}, err error) {
		return secret, nil
	})
	if err != nil {
		return nil, err
	}
	if claims, ok := token.Claims.(*TokenClaims); ok && token.Valid { // 校验token
		return claims, nil
	}
	return nil, errors.New("invalid token")
}

func ParseUserAuthByJwt(authorization string) (*utils.TokenClaims, string, error) {
	// 获取jwt
	parts := strings.SplitN(authorization, " ", 2)
	if len(parts) != 2 || parts[0] != "Bearer" {
		errMsg := fmt.Sprintf("token: %v format err %v", authorization, len(parts))
		return nil, authorization, errors.New(errMsg)
	}
	token := parts[1]

	claims, err := utils.ParseToken(token)
	if err != nil {
		errMsg := fmt.Sprintf("parse token err: %v", err)
		return nil, authorization, errors.New(errMsg)
	}
	return claims, token, nil
}
