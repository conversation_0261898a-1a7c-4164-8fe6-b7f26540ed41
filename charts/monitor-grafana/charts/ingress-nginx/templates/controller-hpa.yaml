{{- if and .Values.controller.autoscaling.enabled (or (eq .Values.controller.kind "Deployment") (eq .Values.controller.kind "Both")) -}}
{{- if not .Values.controller.keda.enabled }}

apiVersion: autoscaling/v2beta2
kind: HorizontalPodAutoscaler
metadata:
  annotations:
  {{- with .Values.controller.autoscaling.annotations }}
  {{- toYaml . | trimSuffix "\n" | nindent 4 }}
  {{- end }}
  labels:
    {{- include "ingress-nginx.labels" . | nindent 4 }}
    app.kubernetes.io/component: controller
  name: {{ include "ingress-nginx.controller.fullname" . }}
  namespace: {{ .Release.Namespace }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ include "ingress-nginx.controller.fullname" . }}
  minReplicas: {{ .Values.controller.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.controller.autoscaling.maxReplicas }}
  metrics:
  {{- with .Values.controller.autoscaling.targetMemoryUtilizationPercentage }}
  - type: Resource 
    resource: 
      name: memory 
      target:
        type: Utilization
        averageUtilization: {{ . }}
  {{- end }}
  {{- with .Values.controller.autoscaling.targetCPUUtilizationPercentage }}
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: {{ . }}
  {{- end }}
  {{- with .Values.controller.autoscalingTemplate }}
{{- toYaml . | nindent 2 }}
  {{- end }}
{{- end }}
{{- end }}

