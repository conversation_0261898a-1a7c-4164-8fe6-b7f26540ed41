{{- $fullName := include "monitor-grafana.fullname" . -}}
{{- if .Values.privateIngressConfig.enable }}
---
apiVersion: v1
kind: Secret
metadata:
  name: private-ingress-secret-{{ $fullName }}
  labels:
{{- include "monitor-grafana.labels" . | nindent 4 }}
data:
{{- with .Values.privateIngressConfig }}
{{- if and .key .cert }}
  tls.crt: {{ .cert | b64enc }}
  tls.key: {{ .key | b64enc}}
{{- else }}
  {{- $ca := genCA "cprom-ca" 36500 }}
  {{- $cert := genSignedCert .host nil nil 36500 $ca }}
  ca.crt: {{ $ca.Cert | b64enc | quote }}
  tls.crt: {{ $cert.Cert | b64enc | quote }}
  tls.key: {{ $cert.Key | b64enc | quote }}
{{- end }}
{{- end }}
---
{{- if semverCompare "<1.14-0" .Capabilities.KubeVersion.GitVersion }}
apiVersion: extensions/v1beta1
{{- else if semverCompare "<1.19-0" .Capabilities.KubeVersion.GitVersion }}
apiVersion: networking.k8s.io/v1beta1
{{- else }}
apiVersion: networking.k8s.io/v1
{{- end }}
kind: Ingress
metadata:
  name: private-ingress-{{ $fullName }}
  labels:
{{- include "monitor-grafana.labels" . | nindent 4 }}
  annotations:
    app.kubernetes.io/component: private-ingress
{{- with .Values.privateIngressConfig.annotations }}
{{ toYaml . | indent 4 }}
{{- end }}
{{- if .Values.privateIngressConfig.httpAccess }}
spec:
  ingressClassName: {{ index .Values "ingress-nginx" "controller" "ingressClass" | quote }}
  rules:
  - http:
      paths:
      - path: /
        backend:
          serviceName: grafana-{{ $fullName }}
          servicePort: 3000
{{- else }}
spec:
  ingressClassName: {{ index .Values "ingress-nginx" "controller" "ingressClass" | quote }}
  tls:
  - secretName: private-ingress-secret-{{ $fullName }}
    hosts:
    - {{ .Values.privateIngressConfig.host }}
  rules:
  - http:
      paths:
      - path: /
        backend:
          serviceName: grafana-{{ $fullName }}
          servicePort: 3000
    host: {{ .Values.privateIngressConfig.host }}
{{- end}}
{{- end}}
