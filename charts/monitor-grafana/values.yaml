fullnameOverride: cprom-test

grafana:
  enable: true
  password: >-
    "test@admin"
  database:
    type: postgres
    host: "ccr-1iz6nf3n.rdsgf9f8yisempv.rds.gz.baidubce.com:3306"
    user: ccr
    password: enterprise123
    dbname: postgres
  image:
    registry: registry.baidubce.com
    image: cce-plugin-dev/grafana
    tag: v7.5.17-beta1
  provisioningImage:
    registry: registry.baidubce.com
    image: cce-plugin-dev/cce-grafana-provisioning
    tag: 2de9ea4
  resources:
    requests:
      cpu: "100m"
      memory: "128Mi"
    limits:
      cpu: "100m"
      memory: "128Mi"
ingress-nginx:
  fullnameOverride: cprom-zihua
  controller:
    name: controller
    image:
      registry: registry.baidubce.com
      image: k8s-mirror/ingress-nginx
      tag: "v0.47.0"
    config:
      ssl-redirect: "true"
      proxy-body-size: "100M"
      whitelist-source-range: ""
      http2-max-concurrent-streams: "100"
      keep-alive-requests: "1000"
    ingressClass: cprom-zihua #需要保持和 helm release 一直
    ingressClassResource:
      enabled: true
    scope:
      enabled: true
    kind: Deployment
    replicaCount: 3
    resources:
      requests:
        cpu: "1"
        memory: "2Gi"
      limits:
        cpu: "1"
        memory: "2Gi"
    service:
      externalTrafficPolicy: "Local"
      annotations:
        service.beta.kubernetes.io/cce-blb-type: appblb
        service.beta.kubernetes.io/cce-load-balancer-internal-vpc: "true"
    admissionWebhooks:
      enabled: false
    metrics:
      enabled: true
      service:
        annotations:
          prometheus.io/scrape: "true"
          prometheus.io/port: "10254"

publicIngressConfig:
  enable: true
  host: "cprom-test.cprom.baidubce.com"
  httpAccess: false
  key: ""
  cert: ""
  annotations: {}

privateIngressConfig:
  enable: true
  host: "cprom-test-vpc.cprom.baidubce.com"
  httpAccess: false
  key: ""
  cert: ""
  annotations: {}
