apiVersion: apps/v1
kind: Deployment
metadata:
  name: control-plane-targets
  namespace: cprom-system
  labels:
    app: control-plane-targets
spec:
  replicas: 1
  selector:
    matchLabels:
      app: control-plane-targets
  template:
    metadata:
      labels:
        app: control-plane-targets
    spec:
      serviceAccount: control-plane-targets
      serviceAccountName: control-plane-targets
      containers:
        - image: registry.baidubce.com/cce-plugin-dev/control-plane-targets:v0.2.0
          args:
            - --namespace=cprom-system
          resources:
            limits:
              cpu: "8"
              memory: 8Gi
            requests:
              cpu: 50m
              memory: 50Mi
          imagePullPolicy: Always
          name: control-plane-targets

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: control-plane-targets
rules:
  - apiGroups:
      - "*"
    resources:
      - services
      - endpoints
      - namespaces
      - configmaps
    verbs:
      - list
      - watch
      - get
      - create
      - update
  - apiGroups:
      - certificates.k8s.io
    resources:
      - "*"
    verbs:
      - "*"
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: control-plane-targets
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: control-plane-targets
subjects:
  - kind: ServiceAccount
    name: control-plane-targets
    namespace: cprom-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: control-plane-targets
  namespace: cprom-system
rules:
  - apiGroups:
      - ""
    resources:
      - namespaces
      - services
      - service
      - endpoints
      - endpoint
    verbs:
      - get
      - list
      - watch
      - update
      - create
      - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: control-plane-targets
  namespace: cprom-system
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: control-plane-targets
subjects:
  - kind: ServiceAccount
    name: control-plane-targets

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: control-plane-targets
  namespace: cprom-system

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: control-plane-metrics-access
rules:
  - nonResourceURLs: ["/metrics"]
    verbs: ["get"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: control-plane-metrics-access
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: control-plane-metrics-access
subjects:
  - kind: User
    name: prometheus
    namespace: cprom-system