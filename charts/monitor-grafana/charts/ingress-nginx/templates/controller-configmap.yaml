apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    {{- include "ingress-nginx.labels" . | nindent 4 }}
    app.kubernetes.io/component: controller
{{- if .Values.controller.configAnnotations }}
  annotations: {{ toYaml .Values.controller.configAnnotations | nindent 4 }}
{{- end }}
  name: {{ include "ingress-nginx.controller.fullname" . }}
  namespace: {{ .Release.Namespace }}
data:
{{- if .Values.controller.addHeaders }}
  add-headers: {{ .Release.Namespace }}/{{ include "ingress-nginx.fullname" . }}-custom-add-headers
{{- end }}
{{- if or .Values.controller.proxySetHeaders .Values.controller.headers }}
  proxy-set-headers: {{ .Release.Namespace }}/{{ include "ingress-nginx.fullname" . }}-custom-proxy-headers
{{- end }}
{{- if .Values.dhParam }}
  ssl-dh-param: {{ printf "%s/%s" .Release.Namespace (include "ingress-nginx.controller.fullname" .) }}
{{- end }}
{{- range $key, $value := .Values.controller.config }}
    {{ $key | nindent 2 }}: {{ $value | quote }}
{{- end }}

