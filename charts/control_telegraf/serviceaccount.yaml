apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{.Release.Namespace}}-serviceaccount
  namespace: {{ .Release.Namespace }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{.Release.Namespace}}-role
  namespace: {{ .Release.Namespace }}
rules:
  - apiGroups: [""]
    resources: ["pods"]
    verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{.Release.Namespace}}-rolebinding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{.Release.Namespace}}-role
subjects:
  - kind: ServiceAccount
    name: {{.Release.Namespace}}-serviceaccount
    namespace: {{ .Release.Namespace }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{.Release.Namespace}}-clusterrole
rules:
  - apiGroups: [""]
    resources: ["nodes"]
    verbs: ["get", "watch", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{.Release.Namespace}}-clusterrolebinding
subjects:
  - kind: ServiceAccount
    name: {{.Release.Namespace}}-serviceaccount
    namespace: {{ .Release.Namespace }}
roleRef:
  kind: ClusterRole
  name: {{.Release.Namespace}}-clusterrole
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{.Release.Namespace}}-telegraf-kubelet
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: system:kubelet-api-admin
subjects:
  - kind: ServiceAccount
    name: {{.Release.Namespace}}-serviceaccount
    namespace: {{.Release.Namespace}}