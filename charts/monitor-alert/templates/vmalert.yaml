{{- $fullName := include "monitor-alert.fullname" . -}}
apiVersion: operator.victoriametrics.com/v1beta1
kind: VMAlert
metadata:
  name: {{ $fullName }}
  namespace: cprom-alert
  labels:
    {{- include "monitor-alert.labels" . | nindent 4 }}
    app.kubernetes.io/component: vmalert
spec:
  podMetadata:
    annotations:
      prometheus.io/port: "8080"
      prometheus.io/scrape: "true"
  image:
    repository: {{.Values.vmalert.image.registry}}/{{.Values.vmalert.image.image}}
    tag: {{.Values.vmalert.image.tag}}
  replicaCount: {{.Values.vmalert.replicaCount}}
  remoteWrite:
    url: {{.Values.remoteWriteUrl}}
  remoteRead:
    url: {{.Values.remoteReadUrl}}
  datasource:
    url: {{.Values.remoteReadUrl}}
  notifier:
    url: http://alert-adaptor-service:8990
  evaluationInterval: 30s
  containers:
    - image: "{{.Values.configReload.image.registry}}/{{.Values.configReload.image.image}}:{{.Values.configReload.image.tag}}"
      name: config-reloader
{{- with .Values.vmalert.resources }}
  resources:
{{- toYaml . | nindent 4 }}
{{- end }}
{{- with .Values.vmalert.extraArgs }}
  extraArgs:
{{- toYaml . | nindent 4 }}
{{- end }}
{{- if .Values.headers }}
    datasource.headers: {{.Values.headers}}
    remoteWrite.headers: {{.Values.headers}}
{{- end }}
{{- with .Values.vmalert.extraEnvs }}
  extraEnvs:
{{- toYaml . | nindent 4 }}
{{- end }}
  ruleSelector:
    matchLabels:
      enable: "true"
      cprom-instance-id: {{.Values.relateInstanceId}}
