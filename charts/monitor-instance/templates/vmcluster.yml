{{- $fullName := include "monitor-instance.fullname" . -}}
apiVersion: operator.victoriametrics.com/v1beta1
kind: VMCluster
metadata:
  name: {{ $fullName }}
  labels:
{{- include "monitor-instance.labels" . | nindent 4 }}
    app.kubernetes.io/component: vmcluster
spec:
  replicationFactor: {{.Values.vmcluster.replicationFactor}}
  retentionPeriod: "{{.Values.vmcluster.retentionPeriod}}"
  vminsert:
{{- with .Values.vmcluster.vminsert.extraArgs }}
    extraArgs:
{{- toYaml . | nindent 6 }}
{{- end }}
{{- with .Values.vmcluster.vminsert.extraEnvs }}
    extraEnvs:
{{- toYaml . | nindent 6 }}
{{- end }}
    image:
      pullPolicy: Always
      repository: {{.Values.vmcluster.vminsert.image.registry}}/{{.Values.vmcluster.vminsert.image.image}}
      tag: {{.Values.vmcluster.vminsert.image.tag}}
    podMetadata:
      annotations:
        prometheus.io/port: "8480"
        prometheus.io/scrape: "true"
    replicaCount: {{.Values.vmcluster.vminsert.replicaCount}}
{{- with .Values.vmcluster.vminsert.resources }}
    resources:
{{- toYaml . | nindent 6 }}
{{- end }}
  vmselect:
{{- with .Values.vmcluster.vmselect.extraArgs }}
    extraArgs:
{{- toYaml . | nindent 6 }}
{{- end }}
{{- with .Values.vmcluster.vmselect.extraEnvs }}
    extraEnvs:
{{- toYaml . | nindent 6 }}
{{- end }}
    image:
      pullPolicy: Always
      repository: {{.Values.vmcluster.vmselect.image.registry}}/{{.Values.vmcluster.vmselect.image.image}}
      tag: {{.Values.vmcluster.vmselect.image.tag}}
    podMetadata:
      annotations:
        prometheus.io/port: "8481"
        prometheus.io/scrape: "true"
    replicaCount: {{.Values.vmcluster.vmselect.replicaCount}}
{{- with .Values.vmcluster.vmselect.resources }}
    resources:
{{- toYaml . | nindent 6 }}
{{- end }}
  vmstorage:
{{- with .Values.vmcluster.vmstorage.extraArgs }}
    extraArgs:
{{- toYaml . | nindent 6 }}
{{- end }}
{{- with .Values.vmcluster.vmstorage.extraEnvs }}
    extraEnvs:
{{- toYaml . | nindent 6 }}
{{- end }}
    image:
      pullPolicy: Always
      repository: {{.Values.vmcluster.vmstorage.image.registry}}/{{.Values.vmcluster.vmstorage.image.image}}
      tag: {{.Values.vmcluster.vmstorage.image.tag}}
    podMetadata:
      annotations:
        prometheus.io/port: "8482"
        prometheus.io/scrape: "true"
    replicaCount: {{.Values.vmcluster.vmstorage.replicaCount}}
{{- with .Values.vmcluster.vmstorage.resources }}
    resources:
{{- toYaml . | nindent 6 }}
{{- end }}
    storage:
      volumeClaimTemplate:
        metadata: {}
        spec:
          resources:
            requests:
              storage: {{.Values.vmcluster.vmstorage.dataVolumeSize}}
          storageClassName: {{.Values.storageClass}}
          volumeMode: Filesystem
        status: {}
    storageDataPath: /vm-data

