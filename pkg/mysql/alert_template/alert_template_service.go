package alert_template

import (
	"errors"
	"time"

	"github.com/jinzhu/gorm"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/mysql"
	"icode.baidu.com/baidu/cprom/cloud-stack/pkg/mysql/model"
)

// 向数据库中插入一条告警模板记录
func Insert(alertTemplate model.AlertTemplate) (err error) {
	alertTemplate.UpdateTime = time.Now() // 无论数据值是否更新，都将update_time更新为当前时间
	result := mysql.ConnTpl.Create(&alertTemplate)
	if result.Error != nil {
		err = result.Error
		return
	}
	return
}

// QueryLimitByPageSize 根据页面大小查询限制，返回总数、模板列表和错误信息
func QueryLimitByPageSize(alertTemplateName string, accountId string, pageSize int64, pageNo int64) (int, []model.AlertTemplate, error) {
	var alertTemplates []model.AlertTemplate
	db := mysql.ConnTpl.Model(&model.AlertTemplate{}).
		Where("alert_template_name like ?", "%"+alertTemplateName+"%").
		Where("bce_account_id = ?", accountId).
		Where("is_delete = ?", 0)
	var totalCount int
	db.Count(&totalCount)
	db.Offset((pageNo - 1) * pageSize).Limit(pageSize).Order("update_time desc").Find(&alertTemplates)
	err := db.Error

	return totalCount, alertTemplates, err
}

// 返回用户所有模板
func QueryAlertTemplates(alertTemplateName string, accountId string) ([]model.AlertTemplate, error) {
	var alertTemplates []model.AlertTemplate
	db := mysql.ConnTpl.Model(&model.AlertTemplate{}).
		Where("bce_account_id = ?", accountId).
		Where("is_delete = ?", 0).
		// Where("alert_template_name like ?", "%"+alertTemplateName+"%").
		Order("update_time desc"). // 添加排序
		Find(&alertTemplates)      // 查询全部结果

	if err := db.Error; err != nil {
		return nil, err
	}
	return alertTemplates, nil
}

// QueryById 根据alertTemplateId和accountId查询AlertTemplate
func QueryById(alertTemplateId string, accountId string) (alertTemplate model.AlertTemplate, err error) {
	result := mysql.ConnTpl.Model(&model.AlertTemplate{}).
		Where("alert_template_id = ?", alertTemplateId).
		Where("bce_account_id = ?", accountId).
		Where("is_delete = ?", 0).
		Find(&alertTemplate)
	if result.Error != nil {
		err = result.Error
		return
	}
	return
}

// QueryByName 查询指定名称的告警模板
func QueryByName(alertTemplateName string, accountId string) (alertTemplate model.AlertTemplate, err error) {
	result := mysql.ConnTpl.Model(&model.AlertTemplate{}).
		Where("alert_template_name = ?", alertTemplateName).
		Where("bce_account_id = ?", accountId).
		Where("is_delete = ?", 0).
		Find(&alertTemplate)
	if result.Error != nil {
		err = result.Error
		return
	}
	return
}

// Update 根据alertTemplateRecord更新对应的AlertTemplate记录
func Update(alertTemplateRecord model.AlertTemplate) (err error) {
	alertTemplateRecord.UpdateTime = time.Now() // 无论数据值是否更新，都将update_time更新为当前时间
	result := mysql.ConnTpl.Model(&model.AlertTemplate{}).
		Where("alert_template_id = ?", alertTemplateRecord.AlertTemplateID).
		Where("bce_account_id = ?", alertTemplateRecord.BCEAccountID).
		Where("is_delete = ?", 0).
		Updates(&alertTemplateRecord)
	if result.Error != nil {
		err = result.Error
		return
	}
	return
}

// Delete 删除一个账户下的指定告警模板，如果成功则返回nil，否则返回错误信息
func Delete(alertTemplateId string, accountId string) (err error) {
	result := mysql.ConnTpl.Model(&model.AlertTemplate{}).
		Where("alert_template_id = ?", alertTemplateId).
		Where("bce_account_id = ?", accountId).
		Update("is_delete", 1)
	if result.Error != nil {
		err = result.Error
		return
	}
	return
}

// InsertMapping 向模板实例映射表中插入一条记录
func InsertMapping(alertTemplateMapping model.TemplateInstanceMapping) (err error) {
	alertTemplateMapping.BindTime = time.Now() // 无论数据值是否更新，都将update_time更新为当前时间
	result := mysql.ConnTpl.Create(&alertTemplateMapping)
	if result.Error != nil {
		err = result.Error
		return
	}
	return
}

// UpdateMapping 根据模板实例映射更新对应的记录
func UpdateMapping(alertTemplateMapping model.TemplateInstanceMapping) (err error) {
	alertTemplateMapping.BindTime = time.Now() // 无论数据值是否更新，都将update_time更新为当前时间
	result := mysql.ConnTpl.Model(&model.TemplateInstanceMapping{}).
		Where("alert_template_id = ?", alertTemplateMapping.AlertTemplateID).
		Where("instance_id = ?", alertTemplateMapping.InstanceID).
		Where("region = ?", alertTemplateMapping.Region).
		Where("bce_account_id = ?", alertTemplateMapping.BCEAccountID).
		Updates(&alertTemplateMapping)
	if result.Error != nil {
		err = result.Error
		return
	}
	return
}

// QueryMappingByID 根据报警模板ID和BCE账号ID查询模板实例映射信息
func QueryMappingByID(alertTemplateId string, accountId string) ([]model.TemplateInstanceMapping, error) {
	var mappings []model.TemplateInstanceMapping
	result := mysql.ConnTpl.Model(&model.TemplateInstanceMapping{}).
		Where("alert_template_id = ?", alertTemplateId).
		Where("bce_account_id = ?", accountId).
		Find(&mappings)
	if result.Error != nil {
		err := result.Error
		return nil, err
	}
	return mappings, nil
}

func QueryMappingByAccountId(accountId string) ([]model.TemplateInstanceMapping, error) {
	var mappings []model.TemplateInstanceMapping
	result := mysql.ConnTpl.Model(&model.TemplateInstanceMapping{}).
		Where("bce_account_id = ?", accountId).
		Limit(2000).
		Find(&mappings)
	if result.Error != nil {
		err := result.Error
		return nil, err
	}
	return mappings, nil
}

// DeleteMapping 删除模板实例映射关系
func DeleteMapping(AlertTemplateID string, InstanceID string, region string, accountID string) (err error) {
	result := mysql.ConnTpl.Model(&model.TemplateInstanceMapping{}).
		Where("alert_template_id = ?", AlertTemplateID).
		Where("instance_id = ?", InstanceID).
		Where("region = ?", region).
		Where("bce_account_id = ?", accountID).
		Unscoped().Delete(&model.TemplateInstanceMapping{})
	if result.Error != nil {
		err = result.Error
		return
	}
	return
}

// DeleteMappingsByAccountAndInstance 根据账户ID和实例ID批量删除映射关系
func BatchDeleteMappingsByInstanceID(InstanceID string, accountID string) (err error) {
	result := mysql.ConnTpl.Model(&model.TemplateInstanceMapping{}).
		Where("instance_id = ?", InstanceID).   // 按实例ID过滤
		Where("bce_account_id = ?", accountID). // 按账户ID过滤
		Unscoped().Delete(&model.TemplateInstanceMapping{})
	if result.Error != nil {
		err = result.Error
		return
	}
	return
}

// QueryByInstanceAndTplID 根据实例ID和模板ID查询告警模板与实例的关联信息
func QueryByInstanceAndTplID(alertTemplateId string, instanceID string, accountId string) (*model.TemplateInstanceMapping, error) {
	var alertTplMapping model.TemplateInstanceMapping
	result := mysql.ConnTpl.Model(&model.TemplateInstanceMapping{}).
		Where("alert_template_id = ?", alertTemplateId).
		Where("bce_account_id = ?", accountId).
		Where("instance_id = ?", instanceID).
		First(&alertTplMapping)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		} else {
			return nil, result.Error
		}
	}
	return &alertTplMapping, nil
}

func QueryByTplID(alertTemplateId string, accountId string) (*model.TemplateInstanceMapping, error) {
	var alertTplMapping model.TemplateInstanceMapping
	result := mysql.ConnTpl.Model(&model.TemplateInstanceMapping{}).
		Where("alert_template_id = ?", alertTemplateId).
		Where("bce_account_id = ?", accountId).
		Limit(2000).
		Find(&alertTplMapping)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		} else {
			return nil, result.Error
		}
	}
	return &alertTplMapping, nil
}

// DeleteMappingByTplIDAndAccountID 删除指定模板ID和账号ID的模板实例映射记录
func DeleteMappingByTplIDAndAccountID(AlertTemplateID string, accountID string) (err error) {
	result := mysql.ConnTpl.Model(&model.TemplateInstanceMapping{}).
		Where("alert_template_id = ?", AlertTemplateID).
		Where("bce_account_id = ?", accountID).
		Unscoped().Delete(&model.TemplateInstanceMapping{})
	if result.Error != nil {
		err = result.Error
		return
	}
	return
}

// DeleteMappingByAlertID 删除指定告警ID模板实例映射记录
func DeleteMappingByAlertIDAndAccountID(AlertRuleID string, accountID string) (err error) {
	result := mysql.ConnTpl.Model(&model.TemplateInstanceMapping{}).
		Where("alert_rule_id = ?", AlertRuleID).
		Where("bce_account_id = ?", accountID).
		Unscoped().Delete(&model.TemplateInstanceMapping{})
	if result.Error != nil {
		err = result.Error
		return
	}
	return
}
