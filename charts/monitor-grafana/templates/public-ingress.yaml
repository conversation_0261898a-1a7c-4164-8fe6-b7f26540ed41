{{- $fullName := include "monitor-grafana.fullname" . -}}
{{- if .Values.publicIngressConfig.enable }}
---
apiVersion: v1
kind: Secret
metadata:
  name: public-ingress-secret-{{ $fullName }}
  labels:
  {{- include "monitor-grafana.labels" . | nindent 4 }}
data:
{{- with .Values.publicIngressConfig }}
{{- if and .key .cert }}
  tls.crt: {{ .cert | b64enc }}
  tls.key: {{ .key | b64enc }}
{{- else }}
  {{- $ca := genCA "cprom-ca" 36500 }}
  {{- $cert := genSignedCert .host nil nil 36500 $ca }}
  ca.crt: {{ $ca.Cert | b64enc | quote }}
  tls.crt: {{ $cert.Cert | b64enc | quote }}
  tls.key: {{ $cert.Key | b64enc | quote }}
{{- end }}
{{- end }}
---
{{- if semverCompare "<1.14-0" .Capabilities.KubeVersion.GitVersion }}
apiVersion: extensions/v1beta1
{{- else if semverCompare "<1.19-0" .Capabilities.KubeVersion.GitVersion }}
apiVersion: networking.k8s.io/v1beta1
{{- else }}
apiVersion: networking.k8s.io/v1
{{- end }}
kind: Ingress
metadata:
  name: public-ingress-{{ $fullName }}
  labels:
{{- include "monitor-grafana.labels" . | nindent 4 }}
  annotations:
    app.kubernetes.io/component: public-ingress
    nginx.ingress.kubernetes.io/configuration-snippet: |
      if ($request_uri ~* "(^|/)(public|login|admin|api)(\.\.|%2e%2e|%5c\.\.|%2f\.\.)(/|$|\?|#)") {
        return 404;
      }
      if ($request_uri ~* "(^|/)(public|login|admin|api)(/|%2f).*(/|%2f)(\.\.|%2e%2e|%5c\.\.)(/|$|\?|#)") {
        return 404;
      }
{{- with .Values.publicIngressConfig.annotations }}
{{ toYaml . | indent 4 }}
{{- end }}
spec:
  ingressClassName: {{ index .Values "ingress-nginx" "controller" "ingressClass" | quote }}
  tls:
  - secretName: cprom-secret-https
    hosts:
    - {{ .Values.publicIngressConfig.host }}
  rules:
  - http:
      paths:
      - path: /
        backend:
          serviceName: grafana-{{ $fullName }}
          servicePort: 3000
    host: {{ .Values.publicIngressConfig.host }}
{{- end}}
